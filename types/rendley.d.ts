declare namespace JSX {
  interface IntrinsicElements {
    'rendley-video-editor': {
      id?: string;
      licensename?: string;
      licensekey?: string;
      pexelsapikey?: string;
      giphyapikey?: string;
      theme?: 'light' | 'dark';
      style?: React.CSSProperties;
      className?: string;
    };
  }
}

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'rendley-video-editor': {
        id?: string;
        licensename?: string;
        licensekey?: string;
        pexelsapikey?: string;
        giphyapikey?: string;
        theme?: 'light' | 'dark';
        style?: React.CSSProperties;
        className?: string;
      };
    }
  }
}

export {};
