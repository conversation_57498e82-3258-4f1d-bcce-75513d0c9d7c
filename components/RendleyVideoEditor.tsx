'use client';

import { useEffect, useRef } from 'react';

interface RendleyVideoEditorProps {
  licenseName: string;
  licenseKey: string;
  pexelsApiKey?: string;
  giphyApiKey?: string;
  theme?: 'light' | 'dark';
  className?: string;
}

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'rendley-video-editor': {
        id?: string;
        licensename?: string;
        licensekey?: string;
        pexelsapikey?: string;
        giphyapikey?: string;
        theme?: 'light' | 'dark';
        style?: React.CSSProperties;
        className?: string;
      };
    }
  }
}

export default function RendleyVideoEditor({
  licenseName,
  licenseKey,
  pexelsApiKey,
  giphyApiKey,
  theme = 'dark',
  className = ''
}: RendleyVideoEditorProps) {
  const rendleyRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const setupRendleyEvents = () => {
      const rendleyElement = document.getElementById('rendley') as any;
      
      if (!rendleyElement) {
        console.log('Rendley element not found, retrying...');
        setTimeout(setupRendleyEvents, 100);
        return;
      }

      console.log('Setting up Rendley event listeners...');

      // Listen for render success and error events
      rendleyElement.addEventListener('onRenderSuccess', (event: CustomEvent) => {
        console.log('Render success:', event.detail);
        const blobUrl = event.detail;
        
        // Handle render success - you can customize this
        if (blobUrl) {
          // Create download link
          const link = document.createElement('a');
          link.href = blobUrl;
          link.download = 'rendered-video.mp4';
          link.click();
        }
      });

      rendleyElement.addEventListener('onRenderError', (event: CustomEvent) => {
        console.error('Render error:', event.detail);
        const message = event.detail;
        
        // Handle render error - you can customize this
        alert(`Render error: ${message}`);
      });

      // Listen for ready event and interact with the engine
      rendleyElement.addEventListener('onReady', async () => {
        console.log('Rendley is ready!');
        
        try {
          const Engine = await rendleyElement.getEngine();
          console.log('Engine loaded:', Engine);

          // Example event handling
          Engine.getInstance().events.on('library:added', (mediaDataId: string) => {
            console.log('Media added to library:', mediaDataId);
            // Handle library added event - you can customize this
          });

          Engine.getInstance().events.on('timeline:updated', (data: any) => {
            console.log('Timeline updated:', data);
            // Handle timeline updates
          });

          Engine.getInstance().events.on('export:started', () => {
            console.log('Export started');
            // Handle export start
          });

          Engine.getInstance().events.on('export:progress', (progress: number) => {
            console.log('Export progress:', progress);
            // Handle export progress
          });

          Engine.getInstance().events.on('export:completed', (result: any) => {
            console.log('Export completed:', result);
            // Handle export completion
          });

        } catch (error) {
          console.error('Error getting engine:', error);
        }
      });

      // Listen for other useful events
      rendleyElement.addEventListener('onError', (event: CustomEvent) => {
        console.error('Rendley error:', event.detail);
      });

      rendleyElement.addEventListener('onLoad', (event: CustomEvent) => {
        console.log('Rendley loaded:', event.detail);
      });
    };

    // Wait for the custom element to be defined
    if (typeof window !== 'undefined') {
      // Small delay to ensure the script has loaded
      setTimeout(setupRendleyEvents, 500);
    }
  }, []);

  return (
    <div className={`rendley-container ${className}`}>
      <rendley-video-editor
        ref={rendleyRef}
        id="rendley"
        licensename={licenseName}
        licensekey={licenseKey}
        pexelsapikey={pexelsApiKey}
        giphyapikey={giphyApiKey}
        theme={theme}
        style={{
          width: '100%',
          height: '600px',
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          display: 'block'
        }}
      />
    </div>
  );
}
