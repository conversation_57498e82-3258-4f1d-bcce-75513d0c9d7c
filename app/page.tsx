import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen p-4 sm:p-8 pb-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-8 items-center">
        <div className="text-center">
          <Image
            className="dark:invert mx-auto mb-4"
            src="/next.svg"
            alt="Next.js logo"
            width={180}
            height={38}
            priority
          />
          <h1 className="text-4xl font-bold mb-4">🎬 Rendley Video Editor</h1>
          <p className="text-gray-600 mb-8 text-lg">
            Professional video editing powered by Rendley SDK
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            ✅ License: mides | Ready to create videos
          </div>
        </div>

        {/* Rendley Video Editor */}
        <div className="w-full max-w-6xl mx-auto">
          <rendley-video-editor
            id="rendley"
            licensename="mides"
            licensekey="54CB577408EA73C950FA0001"
            theme="dark"
            style={{
              width: '100%',
              height: '600px',
              border: '1px solid #e5e7eb',
              borderRadius: '8px'
            }}
          />
        </div>
      </main>
    </div>
  );
}
