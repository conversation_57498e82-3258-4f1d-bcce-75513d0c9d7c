import Image from "next/image";
import RendleyVideoEditor from "../components/RendleyVideoEditor";

export default function Home() {
  return (
    <div className="min-h-screen p-4 sm:p-8 pb-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-8 items-center">
        <div className="text-center">
          <Image
            className="dark:invert mx-auto mb-4"
            src="/next.svg"
            alt="Next.js logo"
            width={180}
            height={38}
            priority
          />
          <h1 className="text-4xl font-bold mb-4">🎬 Rendley Video Editor</h1>
          <p className="text-gray-600 mb-8 text-lg">
            Professional video editing powered by Rendley SDK
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            ✅ License: mides | Ready to create videos
          </div>
        </div>

        {/* Rendley Video Editor with SDK Integration */}
        <div className="w-full max-w-6xl mx-auto">
          <RendleyVideoEditor
            licenseName="mides"
            licenseKey="54CB577408EA73C950FA0001"
            theme="dark"
            className="shadow-lg"
          />
        </div>

        {/* Instructions */}
        <div className="max-w-4xl mx-auto mt-8 p-6 bg-gray-50 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">🚀 SDK Integration Features</h2>
          <ul className="space-y-2 text-sm text-gray-700">
            <li>✅ <strong>Event Handling:</strong> onRenderSuccess, onRenderError, onReady</li>
            <li>✅ <strong>Engine Access:</strong> Full access to Rendley Engine for advanced interactions</li>
            <li>✅ <strong>Library Events:</strong> Track when media is added to library</li>
            <li>✅ <strong>Timeline Events:</strong> Monitor timeline updates and changes</li>
            <li>✅ <strong>Export Events:</strong> Handle export progress and completion</li>
            <li>✅ <strong>Auto Download:</strong> Automatically download rendered videos</li>
          </ul>
          <p className="mt-4 text-xs text-gray-500">
            Check the browser console for detailed event logs and interactions.
          </p>
        </div>
      </main>
    </div>
  );
}
